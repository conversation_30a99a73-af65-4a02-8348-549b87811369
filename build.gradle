plugins {
    id "java"
}

repositories {
    mavenCentral()
}

dependencies {
    testImplementation platform("org.junit:junit-bom:5.10.0")
    testImplementation "org.junit.jupiter:junit-jupiter"
    testImplementation "org.assertj:assertj-core:3.25.1"
}

test {
    useJUnitPlatform()

    testLogging {
        exceptionFormat = "full"
        showStandardStreams = true
        events = ["passed", "failed", "skipped"]
    }
}
